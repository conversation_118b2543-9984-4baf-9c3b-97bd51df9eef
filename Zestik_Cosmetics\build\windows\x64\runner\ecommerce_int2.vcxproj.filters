﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="G:\visionlensapp\Zestik_Cosmetics\windows\runner\flutter_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="G:\visionlensapp\Zestik_Cosmetics\windows\runner\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="G:\visionlensapp\Zestik_Cosmetics\windows\runner\utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="G:\visionlensapp\Zestik_Cosmetics\windows\runner\win32_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="G:\visionlensapp\Zestik_Cosmetics\windows\flutter\generated_plugin_registrant.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="G:\visionlensapp\Zestik_Cosmetics\windows\runner\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="G:\visionlensapp\Zestik_Cosmetics\windows\runner\Runner.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{261E93F8-AC5A-3B2B-A827-09104B87943A}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
