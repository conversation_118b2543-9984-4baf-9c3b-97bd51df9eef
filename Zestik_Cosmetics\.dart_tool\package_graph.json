{"roots": ["ecommerce_int2"], "packages": [{"name": "ecommerce_int2", "version": "1.0.1", "dependencies": ["card_swiper", "country_code_picker", "cupertino_icons", "flutter", "flutter_rating_bar", "flutter_staggered_grid_view", "flutter_svg", "http", "intl", "numberpicker", "pin_code_text_field", "rubber"], "devDependencies": ["build_runner", "flutter_launcher_icons", "flutter_test", "json_serializable"]}, {"name": "flutter_launcher_icons", "version": "0.13.1", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "flutter_svg", "version": "2.1.0", "dependencies": ["flutter", "http", "vector_graphics", "vector_graphics_codec", "vector_graphics_compiler"]}, {"name": "numberpicker", "version": "2.1.2", "dependencies": ["flutter", "infinite_listview"]}, {"name": "intl", "version": "0.18.1", "dependencies": ["clock", "meta", "path"]}, {"name": "flutter_rating_bar", "version": "4.0.1", "dependencies": ["flutter"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "country_code_picker", "version": "3.3.0", "dependencies": ["collection", "diacritic", "flutter"]}, {"name": "pin_code_text_field", "version": "1.8.0", "dependencies": ["flutter"]}, {"name": "rubber", "version": "1.0.1", "dependencies": ["after_layout", "flutter"]}, {"name": "flutter_staggered_grid_view", "version": "0.6.2", "dependencies": ["flutter"]}, {"name": "card_swiper", "version": "3.0.1", "dependencies": ["flutter"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "vector_graphics_compiler", "version": "1.1.17", "dependencies": ["args", "meta", "path", "path_parsing", "vector_graphics_codec", "xml"]}, {"name": "vector_graphics_codec", "version": "1.1.13", "dependencies": []}, {"name": "vector_graphics", "version": "1.1.18", "dependencies": ["flutter", "http", "vector_graphics_codec"]}, {"name": "infinite_listview", "version": "1.1.0", "dependencies": ["flutter"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "http_parser", "version": "4.0.0", "dependencies": ["charcode", "collection", "source_span", "string_scanner", "typed_data"]}, {"name": "diacritic", "version": "0.1.6", "dependencies": []}, {"name": "after_layout", "version": "1.1.0", "dependencies": ["flutter"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}, {"name": "typed_data", "version": "1.3.0", "dependencies": ["collection"]}, {"name": "charcode", "version": "1.3.1", "dependencies": []}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "json_serializable", "version": "6.9.5", "dependencies": ["analyzer", "async", "build", "build_config", "collection", "dart_style", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "source_gen", "version": "2.0.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "pubspec_parse", "version": "1.1.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "glob", "version": "2.0.2", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "file", "version": "6.1.2", "dependencies": ["meta", "path"]}, {"name": "source_helper", "version": "1.3.5", "dependencies": ["analyzer", "collection", "source_gen"]}, {"name": "build", "version": "2.4.2", "dependencies": ["analyzer", "async", "convert", "crypto", "glob", "logging", "meta", "package_config", "path"]}, {"name": "logging", "version": "1.0.2", "dependencies": []}, {"name": "crypto", "version": "3.0.1", "dependencies": ["collection", "typed_data"]}, {"name": "convert", "version": "3.0.1", "dependencies": ["typed_data"]}, {"name": "timing", "version": "1.0.0", "dependencies": ["json_annotation"]}, {"name": "stream_transform", "version": "2.0.0", "dependencies": []}, {"name": "shelf", "version": "1.2.0", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pool", "version": "1.5.0", "dependencies": ["async", "stack_trace"]}, {"name": "io", "version": "1.0.3", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.0.1", "dependencies": ["async"]}, {"name": "build_daemon", "version": "4.0.0", "dependencies": ["built_collection", "built_value", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "web_socket_channel", "version": "2.1.0", "dependencies": ["async", "crypto", "stream_channel"]}, {"name": "shelf_web_socket", "version": "1.0.1", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "built_value", "version": "8.1.3", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "fixnum", "version": "1.0.0", "dependencies": []}, {"name": "dart_style", "version": "2.3.8", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span"]}, {"name": "macros", "version": "0.1.3-main.0", "dependencies": ["_macros"]}, {"name": "_macros", "version": "0.3.3", "dependencies": []}, {"name": "watcher", "version": "1.1.1", "dependencies": ["async", "path"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "analyzer", "version": "6.11.0", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "macros", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "76.0.0", "dependencies": ["meta"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "build_runner", "version": "2.4.13", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web_socket_channel", "yaml"]}, {"name": "build_runner_core", "version": "7.2.2", "dependencies": ["async", "build", "build_config", "build_resolvers", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "mime", "version": "1.0.1", "dependencies": []}, {"name": "js", "version": "0.6.3", "dependencies": []}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "image", "version": "4.2.0", "dependencies": ["archive", "meta", "xml"]}, {"name": "archive", "version": "3.4.2", "dependencies": ["crypto", "path", "pointycastle"]}, {"name": "pointycastle", "version": "3.9.1", "dependencies": ["collection", "convert", "js"]}, {"name": "build_resolvers", "version": "2.2.1", "dependencies": ["analyzer", "async", "build", "collection", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform", "yaml"]}], "configVersion": 1}